/**
 * Service for poster generation and editing operations
 */

const API_BASE_URL = "/api/posters";

export interface PosterGenerationOptions {
  prompt: string;
  size?: string;
  resolution?: string;
  aspect_ratio?: string;
  rendering_speed?: string;
  magic_prompt?: string;
  negative_prompt?: string;
  num_images?: number;
  seed?: number;
  style_type?: string;
}

export interface PosterResponse {
  success: boolean;
  image_url?: string;
  images?: string[];
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

export interface ReferenceEditOptions {
  prompt: string;
  referenceImages: File[];
  size?: string;
}

export interface MaskEditOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface MultiTurnEditOptions {
  previousResponseId: string;
  editPrompt: string;
}

/**
 * Ensure image URLs are properly formatted with full domain
 */
function ensureFullImageUrl(url: string): string {
  if (url.startsWith('data:')) {
    return url; // Base64 images don't need modification
  }
  if (url.startsWith('/')) {
    return `${window.location.origin}${url}`;
  }
  if (!url.startsWith('http')) {
    return `${window.location.origin}/${url}`;
  }
  return url;
}

/**
 * Validate image file type and size
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!validTypes.includes(file.type)) {
    return {
      valid: false,
      error: 'Tipo de archivo no válido. Solo se permiten imágenes JPEG, PNG y WebP.'
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'El archivo es demasiado grande. El tamaño máximo es 10MB.'
    };
  }

  return { valid: true };
}

/**
 * Generate an initial poster with Ideogram AI
 */
export async function generatePoster(options: PosterGenerationOptions): Promise<PosterResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("size", options.size || "1024x1024");

    // Add Ideogram-specific parameters
    if (options.resolution) {
      formData.append("resolution", options.resolution);
    }
    if (options.aspect_ratio) {
      formData.append("aspect_ratio", options.aspect_ratio);
    }
    if (options.rendering_speed) {
      formData.append("rendering_speed", options.rendering_speed);
    }
    if (options.magic_prompt) {
      formData.append("magic_prompt", options.magic_prompt);
    }
    if (options.negative_prompt) {
      formData.append("negative_prompt", options.negative_prompt);
    }
    if (options.num_images) {
      formData.append("num_images", options.num_images.toString());
    }
    if (options.seed !== undefined) {
      formData.append("seed", options.seed.toString());
    }
    if (options.style_type) {
      formData.append("style_type", options.style_type);
    }

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error generating poster:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al generar el póster"
    };
  }
}

/**
 * Edit poster using reference images
 */
export async function editWithReferences(options: ReferenceEditOptions): Promise<PosterResponse> {
  try {
    // Validate reference images
    for (const file of options.referenceImages) {
      const validation = validateImageFile(file);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        };
      }
    }

    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Máximo 4 imágenes de referencia permitidas"
      };
    }

    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("size", options.size || "auto");
    
    options.referenceImages.forEach((file, index) => {
      formData.append("reference_images", file);
    });

    const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error editing with references:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al editar con referencias"
    };
  }
}

/**
 * Edit poster using mask
 */
export async function editWithMask(options: MaskEditOptions): Promise<PosterResponse> {
  try {
    // Validate image files
    const imageValidation = validateImageFile(options.image);
    if (!imageValidation.valid) {
      return {
        success: false,
        error: `Error en imagen: ${imageValidation.error}`
      };
    }

    const maskValidation = validateImageFile(options.mask);
    if (!maskValidation.valid) {
      return {
        success: false,
        error: `Error en máscara: ${maskValidation.error}`
      };
    }

    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetch(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error editing with mask:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido al editar con máscara"
    };
  }
}

/**
 * Multi-turn edit of existing poster
 */
export async function multiTurnEdit(options: MultiTurnEditOptions): Promise<PosterResponse> {
  try {
    const formData = new FormData();
    formData.append("previous_response_id", options.previousResponseId);
    formData.append("edit_prompt", options.editPrompt);

    const response = await fetch(`${API_BASE_URL}/multi-turn-edit`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in multi-turn edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido en edición multi-turn"
    };
  }
}

/**
 * Stream poster generation with partial images
 */
export async function* streamPosterGeneration(prompt: string): AsyncGenerator<any, void, unknown> {
  try {
    const formData = new FormData();
    formData.append("prompt", prompt);

    const response = await fetch(`${API_BASE_URL}/stream-generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No response body reader available");
    }

    const decoder = new TextDecoder();
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6));
              
              // Ensure image URLs are properly formatted
              if (data.success && data.image_url) {
                data.image_url = ensureFullImageUrl(data.image_url);
              }
              
              yield data;
            } catch (e) {
              console.warn("Failed to parse streaming data:", line);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  } catch (error) {
    console.error("Error in streaming generation:", error);
    yield {
      success: false,
      error: error instanceof Error ? error.message : "Error desconocido en generación streaming"
    };
  }
}
