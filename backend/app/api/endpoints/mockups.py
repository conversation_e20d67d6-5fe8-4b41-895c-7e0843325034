"""
API endpoints for product mockup generation using Ideogram 3.0 Quality exclusively.
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, Form, UploadFile, File
from typing import Optional, List
import json

from app.core.auth import verify_api_key
from app.services.mockup_service import mockup_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/contexts")
async def get_mockup_contexts():
    """Get all available mockup contexts."""
    try:
        contexts = mockup_service.get_available_contexts()
        return {
            "success": True,
            "contexts": contexts,
            "total_contexts": len(contexts)
        }
    except Exception as e:
        logger.error(f"Error getting mockup contexts: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting contexts: {str(e)}"
        )


@router.post(
    "/generate",
    dependencies=[Depends(verify_api_key)],
)
async def generate_mockup(
    product_image: UploadFile = File(..., description="Product image to place in mockup"),
    context: str = Form(..., description="Mockup context (hands, desk, lifestyle, outdoor, studio, social)"),
    product_description: str = Form(default="", description="Description of the product"),
    size: str = Form(default="1024x1024", description="Image size (1024x1024, 1024x1792, 1792x1024)"),
    variations: int = Form(default=4, description="Number of variations to generate (minimum 4)")
):
    """Generate multiple product mockup variations using Ideogram 3.0 Quality exclusively."""
    
    try:
        logger.info(f"🎨 Generating {variations} {context} mockup variations for product: {product_description[:50]}...")

        # Validate file type
        if not product_image.content_type or not product_image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="File must be an image"
            )

        # Generate mockup variations
        result = await mockup_service.generate_mockup(
            product_image=product_image,
            context=context,
            product_description=product_description,
            size=size,
            variations=max(4, variations)  # Ensure minimum 4 variations
        )

        if result.get("success"):
            return {
                "success": True,
                "variations": result.get("variations", []),
                "total_generated": result.get("total_generated", 0),
                "context": context,
                "context_name": result.get("context_name", ""),
                "product_description": product_description,
                "metadata": result.get("metadata", {}),
                "message": f"Generadas {result.get('total_generated', 0)} variaciones de mockup en contexto: {context}"
            }
        else:
            raise HTTPException(
                status_code=500,
                detail=result.get("error", "Error generating mockup variations")
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_mockup endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during mockup generation: {e}"
        )


@router.post(
    "/generate-multiple",
    dependencies=[Depends(verify_api_key)],
)
async def generate_multiple_mockups(
    product_image: UploadFile = File(..., description="Product image to place in mockups"),
    contexts: str = Form(..., description="JSON array of mockup contexts"),
    product_description: str = Form(default="", description="Description of the product"),
    size: str = Form(default="1024x1024", description="Image size")
):
    """Generate multiple product mockups with different contexts."""
    
    try:
        logger.info(f"🎨 Generating multiple mockups for product: {product_description[:50]}...")

        # Validate file type
        if not product_image.content_type or not product_image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="File must be an image"
            )

        # Parse contexts JSON
        try:
            contexts_list = json.loads(contexts)
            if not isinstance(contexts_list, list):
                raise ValueError("Contexts must be a list")
        except (json.JSONDecodeError, ValueError) as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid contexts format: {e}"
            )

        # Generate multiple mockups
        result = await mockup_service.generate_multiple_mockups(
            product_image=product_image,
            contexts=contexts_list,
            product_description=product_description,
            size=size
        )
        
        return {
            "success": True,
            "results": result.get("results", {}),
            "total_generated": result.get("total_generated", 0),
            "contexts_processed": result.get("contexts_processed", []),
            "product_description": product_description,
            "message": f"Generados {result.get('total_generated', 0)} mockups exitosamente"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in generate_multiple_mockups endpoint: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error during multiple mockup generation: {e}"
        )


@router.post(
    "/preview-contexts",
    dependencies=[Depends(verify_api_key)],
)
async def preview_contexts_for_product(
    product_description: str = Form(..., description="Description of the product")
):
    """Get context suggestions based on product description."""
    
    try:
        # Get all available contexts
        all_contexts = mockup_service.get_available_contexts()
        
        # Enhanced intelligent logic to suggest best contexts based on product type
        product_lower = product_description.lower()

        suggested_contexts = []
        confidence_scores = {}
        reasoning = ""

        # Tech products - Electronics, gadgets, devices
        tech_keywords = ['phone', 'smartphone', 'laptop', 'tablet', 'computer', 'tech', 'device', 'gadget', 'headphones', 'earbuds', 'smartwatch', 'camera', 'gaming', 'console', 'keyboard', 'mouse']
        if any(word in product_lower for word in tech_keywords):
            suggested_contexts = ['hands', 'desk', 'lifestyle', 'studio']
            confidence_scores = {'hands': 0.95, 'desk': 0.90, 'lifestyle': 0.85, 'studio': 0.80}
            reasoning = "Productos tecnológicos se ven mejor en manos de usuarios, escritorios profesionales y contextos de estilo de vida"

        # Fashion/accessories - Clothing, jewelry, accessories
        fashion_keywords = ['clothing', 'shirt', 'dress', 'pants', 'jacket', 'accessory', 'jewelry', 'watch', 'bag', 'shoes', 'hat', 'sunglasses', 'belt', 'scarf', 'fashion']
        elif any(word in product_lower for word in fashion_keywords):
            suggested_contexts = ['lifestyle', 'social', 'outdoor', 'studio']
            confidence_scores = {'lifestyle': 0.95, 'social': 0.90, 'outdoor': 0.85, 'studio': 0.80}
            reasoning = "Productos de moda lucen mejor en contextos sociales, de estilo de vida y ambientes exteriores"

        # Food/beverages - Consumables, drinks, snacks
        food_keywords = ['food', 'drink', 'coffee', 'tea', 'snack', 'beverage', 'juice', 'water', 'beer', 'wine', 'restaurant', 'cafe', 'meal', 'breakfast', 'lunch', 'dinner']
        elif any(word in product_lower for word in food_keywords):
            suggested_contexts = ['hands', 'lifestyle', 'social', 'outdoor']
            confidence_scores = {'hands': 0.95, 'lifestyle': 0.90, 'social': 0.88, 'outdoor': 0.75}
            reasoning = "Productos alimenticios se ven mejor siendo consumidos en manos, contextos sociales y de estilo de vida"

        # Sports/fitness - Athletic gear, equipment
        sports_keywords = ['sport', 'fitness', 'exercise', 'gym', 'workout', 'athletic', 'running', 'yoga', 'training', 'outdoor', 'hiking', 'cycling', 'swimming']
        elif any(word in product_lower for word in sports_keywords):
            suggested_contexts = ['outdoor', 'lifestyle', 'hands', 'social']
            confidence_scores = {'outdoor': 0.95, 'lifestyle': 0.88, 'hands': 0.85, 'social': 0.80}
            reasoning = "Productos deportivos lucen mejor en ambientes exteriores, contextos activos y de estilo de vida"

        # Beauty/cosmetics - Personal care, makeup, skincare
        beauty_keywords = ['beauty', 'cosmetic', 'makeup', 'skincare', 'perfume', 'lotion', 'cream', 'serum', 'lipstick', 'foundation', 'shampoo', 'soap', 'spa']
        elif any(word in product_lower for word in beauty_keywords):
            suggested_contexts = ['hands', 'lifestyle', 'studio', 'social']
            confidence_scores = {'hands': 0.95, 'lifestyle': 0.88, 'studio': 0.85, 'social': 0.80}
            reasoning = "Productos de belleza se ven mejor en manos, estudios profesionales y contextos de estilo de vida"

        # Home/decor - Furniture, home goods
        home_keywords = ['home', 'furniture', 'decor', 'lamp', 'chair', 'table', 'kitchen', 'bedroom', 'living', 'decoration', 'vase', 'candle', 'pillow']
        elif any(word in product_lower for word in home_keywords):
            suggested_contexts = ['lifestyle', 'studio', 'desk', 'social']
            confidence_scores = {'lifestyle': 0.95, 'studio': 0.88, 'desk': 0.80, 'social': 0.75}
            reasoning = "Productos para el hogar lucen mejor en contextos de estilo de vida y estudios profesionales"

        # Books/education - Learning materials
        education_keywords = ['book', 'notebook', 'pen', 'pencil', 'education', 'learning', 'study', 'school', 'university', 'course', 'manual']
        elif any(word in product_lower for word in education_keywords):
            suggested_contexts = ['desk', 'hands', 'lifestyle', 'studio']
            confidence_scores = {'desk': 0.95, 'hands': 0.88, 'lifestyle': 0.80, 'studio': 0.75}
            reasoning = "Productos educativos se ven mejor en escritorios, manos y contextos de estudio"

        # Automotive - Car accessories, tools
        auto_keywords = ['car', 'auto', 'vehicle', 'tire', 'engine', 'automotive', 'driving', 'garage', 'mechanic', 'tool']
        elif any(word in product_lower for word in auto_keywords):
            suggested_contexts = ['hands', 'outdoor', 'lifestyle', 'studio']
            confidence_scores = {'hands': 0.90, 'outdoor': 0.88, 'lifestyle': 0.80, 'studio': 0.75}
            reasoning = "Productos automotrices lucen mejor en manos, ambientes exteriores y contextos prácticos"

        # Default suggestions with lower confidence
        else:
            suggested_contexts = ['hands', 'desk', 'lifestyle', 'studio']
            confidence_scores = {'hands': 0.70, 'desk': 0.65, 'lifestyle': 0.75, 'studio': 0.70}
            reasoning = "Sugerencias generales basadas en versatilidad de contextos"
        
        # Build response with suggested contexts and confidence scores
        suggested = {}
        for context_id in suggested_contexts:
            if context_id in all_contexts:
                suggested[context_id] = {
                    **all_contexts[context_id],
                    "confidence_score": confidence_scores.get(context_id, 0.5),
                    "recommended": confidence_scores.get(context_id, 0.5) >= 0.8
                }

        # Sort by confidence score
        suggested = dict(sorted(suggested.items(), key=lambda x: x[1]["confidence_score"], reverse=True))

        return {
            "success": True,
            "product_description": product_description,
            "suggested_contexts": suggested,
            "all_contexts": all_contexts,
            "total_suggested": len(suggested),
            "reasoning": reasoning,
            "confidence_scores": confidence_scores,
            "analysis": {
                "product_type_detected": True if reasoning != "Sugerencias generales basadas en versatilidad de contextos" else False,
                "top_recommendation": list(suggested.keys())[0] if suggested else None,
                "high_confidence_count": len([c for c in confidence_scores.values() if c >= 0.8])
            }
        }
        
    except Exception as e:
        logger.error(f"Error in preview_contexts endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting context suggestions: {str(e)}"
        )
