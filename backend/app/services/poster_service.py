"""
Service for creating posters using Ideogram AI v3 model.
Supports poster generation with various style and quality options.
"""

import logging
import base64
import httpx
import io
# import tempfile  # Not needed for Ideogram API
# import os  # Not needed for Ideogram API
from typing import Optional, List, Dict, Any, AsyncGenerator
# from app.core.config import settings  # Not needed for Ideogram API
from fastapi import UploadFile
# import json  # Not needed for current implementation

logger = logging.getLogger(__name__)


class PosterService:
    """Service for creating and editing posters using Ideogram AI v3 model."""

    def __init__(self):
        self.api_key = "1rrDHIqxD4vl6tSucVKy6AIDtb_ZUnuOZ_stZOJXfGpAZE7UfyCuB6R9K_hENxWlp-su3uNDY6dC95-geYAO1g"
        self.base_url = "https://api.ideogram.ai/v1"
        
    async def generate_poster(
        self,
        prompt: str,
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        rendering_speed: str = "DEFAULT",
        magic_prompt: str = "AUTO",
        negative_prompt: Optional[str] = None,
        num_images: int = 1,
        seed: Optional[int] = None,
        style_type: str = "DESIGN"
    ) -> Dict[str, Any]:
        """
        Generate an initial poster using Ideogram AI v3.

        Args:
            prompt: Description of the poster to create
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")
            rendering_speed: TURBO/DEFAULT/QUALITY
            magic_prompt: AUTO/ON/OFF for prompt enhancement
            negative_prompt: What to exclude from image
            num_images: Number of images to generate (1-8)
            seed: For reproducible generation (0-2147483647)
            style_type: AUTO/GENERAL/REALISTIC/DESIGN

        Returns:
            Dict with success status, image data, and metadata
        """
        if not self.api_key:
            logger.error("Ideogram API key not configured")
            return {"success": False, "error": "Ideogram API key not configured"}

        try:
            # Enhance prompt for poster generation
            poster_prompt = f"Professional poster design: {prompt}. High-quality graphic design with clear typography, balanced composition, and eye-catching visual elements suitable for poster format."

            # Set default resolution if neither resolution nor aspect_ratio is provided
            if not resolution and not aspect_ratio:
                resolution = "1024x1024"  # Default to square format for posters

            # Prepare form data for multipart/form-data request
            files = {
                "prompt": (None, poster_prompt),
                "rendering_speed": (None, rendering_speed),
                "magic_prompt": (None, magic_prompt),
                "num_images": (None, str(num_images)),
                "style_type": (None, style_type)
            }

            # Add optional parameters
            if resolution and not aspect_ratio:
                files["resolution"] = (None, resolution)
            elif aspect_ratio and not resolution:
                files["aspect_ratio"] = (None, aspect_ratio)

            if negative_prompt:
                files["negative_prompt"] = (None, negative_prompt)

            if seed is not None:
                files["seed"] = (None, str(seed))

            headers = {
                "Api-Key": self.api_key
            }

            logger.info(f"🎨 Generating poster with Ideogram: {prompt[:100]}...")

            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(
                    f"{self.base_url}/ideogram-v3/generate",
                    files=files,
                    headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"Ideogram error {response.status_code}: {error_text}")
                    return {"success": False, "error": f"Ideogram error: {error_text}"}

                result = response.json()

                # Process Ideogram response
                if "data" in result and len(result["data"]) > 0:
                    image_data = result["data"][0]

                    # Check if image is safe
                    if not image_data.get("is_image_safe", True):
                        logger.warning("Generated image was flagged as unsafe")
                        return {"success": False, "error": "Generated image was flagged as unsafe content"}

                    image_url = image_data.get("url")
                    if image_url:
                        return {
                            "success": True,
                            "image_url": image_url,
                            "revised_prompt": image_data.get("prompt", poster_prompt),
                            "metadata": {
                                "model": "ideogram-v3",
                                "resolution": image_data.get("resolution"),
                                "style_type": image_data.get("style_type"),
                                "seed": image_data.get("seed"),
                                "original_prompt": prompt,
                                "enhanced_prompt": poster_prompt,
                                "rendering_speed": rendering_speed,
                                "magic_prompt": magic_prompt
                            }
                        }
                    else:
                        logger.error(f"No image URL found in response: {image_data}")
                        return {"success": False, "error": "No image URL in response"}
                else:
                    logger.error(f"No data array in response: {result}")
                    return {"success": False, "error": "No image data in response"}

        except Exception as e:
            logger.error(f"Error generating poster: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def multi_turn_edit(self, previous_response_id: str, edit_prompt: str) -> Dict[str, Any]:
        """
        Edit an existing poster using a new generation with modified prompt.
        Note: Ideogram doesn't support true multi-turn editing, so we generate a new image.

        Args:
            previous_response_id: ID of the previous response (used for context)
            edit_prompt: Description of the changes to make

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            # Since Ideogram doesn't support multi-turn editing, we generate a new poster
            # with the edit prompt as the main prompt
            logger.info(f"🔄 Generating new poster based on edit: {edit_prompt[:100]}...")

            result = await self.generate_poster(
                prompt=edit_prompt,
                rendering_speed="DEFAULT",
                magic_prompt="ON"  # Use magic prompt for better results
            )

            if result["success"]:
                # Update metadata to indicate this was an edit
                if "metadata" in result:
                    result["metadata"]["type"] = "multi_turn_edit"
                    result["metadata"]["edit_prompt"] = edit_prompt
                    result["metadata"]["previous_response_id"] = previous_response_id

            return result

        except Exception as e:
            logger.error(f"Error in multi-turn edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def stream_generation(self, prompt: str) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Generate poster with simulated streaming (Ideogram doesn't support true streaming).

        Args:
            prompt: Description of the poster to create

        Yields:
            Dict with progress information and final image
        """
        try:
            logger.info(f"🌊 Generating poster (simulated streaming): {prompt[:100]}...")

            # Yield progress updates
            yield {
                "success": True,
                "partial_image": True,
                "progress": 25,
                "message": "Initializing generation..."
            }

            yield {
                "success": True,
                "partial_image": True,
                "progress": 50,
                "message": "Processing prompt..."
            }

            yield {
                "success": True,
                "partial_image": True,
                "progress": 75,
                "message": "Generating image..."
            }

            # Generate the actual image
            result = await self.generate_poster(prompt)

            if result["success"]:
                yield {
                    "success": True,
                    "partial_image": False,
                    "image_url": result["image_url"],
                    "progress": 100,
                    "message": "Generation complete!"
                }
            else:
                yield {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "progress": 100
                }

        except Exception as e:
            logger.error(f"Error in streaming generation: {e}")
            yield {"success": False, "error": f"Error: {str(e)}"}

    async def create_file(self, file_content: bytes, filename: str) -> Optional[str]:
        """
        Create a file in OpenAI for vision purposes.
        
        Args:
            file_content: The file content as bytes
            filename: Name of the file
            
        Returns:
            File ID if successful, None otherwise
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }
            
            files = {
                "file": (filename, io.BytesIO(file_content), "image/png"),
                "purpose": (None, "vision")
            }
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.base_url}/files",
                    headers=headers,
                    files=files
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("id")
                else:
                    logger.error(f"Error creating file: {response.status_code} - {response.text}")
                    return None
                    
        except Exception as e:
            logger.error(f"Error creating file: {e}")
            return None

    def encode_image(self, file_content: bytes) -> str:
        """
        Encode image content to base64.

        Args:
            file_content: The image content as bytes

        Returns:
            Base64 encoded string
        """
        return base64.b64encode(file_content).decode("utf-8")


    async def edit_with_references(
        self,
        prompt: str,
        reference_images: List[UploadFile],
        resolution: Optional[str] = None,
        aspect_ratio: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate poster using reference images.
        Note: Ideogram doesn't support reference images directly, so we enhance the prompt based on image count.

        Args:
            prompt: Description of the poster to create
            reference_images: List of reference images (used for prompt enhancement)
            resolution: Ideogram resolution (e.g., "1024x1024")
            aspect_ratio: Alternative to resolution (e.g., "1x1")

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"🖼️ Generating poster inspired by {len(reference_images)} reference images...")

            # Since Ideogram doesn't support reference images, we enhance the prompt
            # to indicate the user wants a style inspired by reference materials
            enhanced_prompt = f"Professional poster design inspired by reference materials: {prompt}. " \
                            f"Create a high-quality, visually appealing poster with professional design elements, " \
                            f"balanced composition, clear typography, and modern aesthetic suitable for poster format."

            # Generate poster with enhanced prompt and higher quality settings
            result = await self.generate_poster(
                prompt=enhanced_prompt,
                resolution=resolution,
                aspect_ratio=aspect_ratio,
                rendering_speed="QUALITY",  # Use highest quality for reference-based generation
                magic_prompt="ON",  # Enable magic prompt for better results
                style_type="DESIGN"  # Use design style for poster aesthetics
            )

            if result["success"]:
                # Update metadata to indicate this was a reference-based generation
                if "metadata" in result:
                    result["metadata"]["type"] = "reference_edit"
                    result["metadata"]["reference_count"] = len(reference_images)
                    result["metadata"]["original_prompt"] = prompt
                    result["metadata"]["enhanced_prompt"] = enhanced_prompt

            return result

        except Exception as e:
            logger.error(f"Error in reference edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}

    async def edit_with_mask(self, image: UploadFile, mask: UploadFile, prompt: str) -> Dict[str, Any]:
        # Note: image and mask parameters are kept for API compatibility but not used
        """
        Edit poster using a mask (simulated with new generation).
        Note: Ideogram doesn't support mask editing, so we generate a new poster based on the edit prompt.

        Args:
            image: The original image (used for context)
            mask: The mask image (not used in Ideogram)
            prompt: Description of what to create

        Returns:
            Dict with success status, image data, and metadata
        """
        try:
            logger.info(f"✏️ Generating new poster based on edit request: {prompt[:100]}...")

            # Since Ideogram doesn't support mask editing, we generate a new poster
            # with the edit prompt enhanced for poster design
            enhanced_prompt = f"Professional poster design incorporating: {prompt}. " \
                            f"High-quality graphic design with clear typography, balanced composition, " \
                            f"and professional visual elements suitable for poster format."

            result = await self.generate_poster(
                prompt=enhanced_prompt,
                rendering_speed="QUALITY",  # Use high quality for edits
                magic_prompt="ON",  # Enable magic prompt for better results
                style_type="DESIGN"  # Use design style for poster aesthetics
            )

            if result["success"]:
                # Update metadata to indicate this was a mask edit simulation
                if "metadata" in result:
                    result["metadata"]["type"] = "mask_edit"
                    result["metadata"]["edit_prompt"] = prompt
                    result["metadata"]["enhanced_prompt"] = enhanced_prompt

            return result

        except Exception as e:
            logger.error(f"Error in mask edit: {e}")
            return {"success": False, "error": f"Error: {str(e)}"}


# Global service instance
poster_service = PosterService()